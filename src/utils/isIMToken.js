import { IMUIKit } from '@xkit-yx/im-kit-ui';
import Vue from 'vue';
// import { getMsgContentTipByType } from '@xkit-yx/im-kit-ui/es/common';
import vueStore from '@/store/index.js';
import { getFlowState } from '@/api/kf.js';
import { getImInfo } from '@/api/iminfo.js';
import _ from 'lodash';
import { getOrderDetail } from '@/api/confirmOrder.js';
let hasInit = false;
const getDetailForCard = (teamId) => {
  getFlowState({ teamId }).then((res) => {
    if (res.code == 200) {
      const result = res.data;
      vueStore.dispatch('setFlowState', result);
      const orderId = result.orderId;
      if (orderId) {
        getOrderDetail(orderId).then((res) => {
          if (res.code == 200) {
            vueStore.dispatch('setImOrderDetail', res.data);
          }
        });
      } else {
        vueStore.dispatch('setImOrderDetail', {});
      }
    }
  });
};
const throttledGetDetailForCard = _.throttle((sessionId) => {
  const teamId = sessionId.replace('team-', '');
  getDetailForCard(teamId);
}, 2000);
const initIM = (yximtoken) => {
  return new Promise(async (resolve, reject) => {
    const initOptions = {
      debugLevel: 'off',
      authType: 1,
      // appkey: '7eec32972b9d966179c5acee4c6f0141',
      appkey: 'b14909500c44a20d3b8b252fab213ca2', // 请填写你的appkey
      account: yximtoken.account, // 请填写你的account
      token: yximtoken.token, // 请填写你的token
    };
    const localOptions = {
      // 添加好友模式，默认需要验证
      addFriendNeedVerify: false,
      // 群组被邀请模式，默认不需要验证
      teamBeInviteMode: 'noVerify',
      // 单聊消息是否显示已读未读 默认 false
      p2pMsgReceiptVisible: true,
      // 群聊消息是否显示已读未读 默认 false
      teamMsgReceiptVisible: true,
      // 是否需要@消息 默认 true
      needMention: true,
      // 是否显示在线离线状态 默认 true
      loginStateVisible: true,
      // 是否允许转让群主
      allowTransferTeamOwner: true,
      sendMsgBefore: (options, type) => {
        const { store } = window.__xkit_store__;
        // const pushContent = getMsgContentTipByType({
        //   body: options.body || '自定义消息',
        //   type,
        // });
        const yxAitMsg = options.ext
          ? options.ext.yxAitMsg
          : { forcePushIDsList: '[]', needForcePush: false };

        // 如果是 at 消息，需要走离线强推
        const { forcePushIDsList, needForcePush } = yxAitMsg
          ? // @ts-ignore
            store.msgStore._formatExtAitToPushInfo(yxAitMsg, options.body)
          : { forcePushIDsList: '[]', needForcePush: false };

        console.log('forcePushIDsList: ', forcePushIDsList);

        const pushPayload = JSON.stringify({
          pushTitle: '您收到一条新的消息提醒，请注意查看',
        });
        const pushInfo = {
          needPush: true,
          needPushBadge: true,
          pushPayload,
          needForcePush,
          forcePushIDsList,
        };
        if (options.ext) {
          try {
            let ext = JSON.parse(options.ext);
            if (ext.noyidun == 1) {
              options.antiSpamUsingYidun = false;
            }
          } catch (e) {}
        }
        const result = { ...options, pushInfo };
        return result;
      },
    };
    const funcOptions = {
      db: true,
      onsyncdone: () => {
        const { nim, store } = window.__xkit_store__;
        let accounts = [];
        store.sessionStore.sessions.forEach((ele) => {
          if (ele.id.indexOf('p2p') == 0) {
            accounts.push(ele.id.replace('p2p-', ''));
          }
        });
        nim.nim.getUsers({
          accounts,
          sync: true,
          done: (err, result) => {
            vueStore.dispatch('SetNickList', result);
            const sessionNames = document.querySelectorAll(
              '.conversation-item-content-name-forjs',
            );
            sessionNames.forEach((ele) => {
              let account = ele.innerText || '';
              let findIt = result.find((item) => item.account == account);
              const name = findIt && findIt.nick;
              if (name && name !== account) {
                ele.innerText = name;
              }
            });
          },
        });

        // setTimeout(() => {
        //   const sessionNames = document.querySelectorAll(
        //     '.conversation-item-content-name-forjs',
        //   );
        //   const { store } = window.__xkit_store__;
        //   sessionNames.forEach((ele) => {
        //     let account = ele.innerText || '';
        //     let name = store.uiStore.getAppellation({ account });
        //     if (name && name !== account) {
        //       ele.innerText = name;
        //     }
        //   });
        // }, 3000);
      },
      onmsg: (msg) => {
        const { sessionId = '' } = msg;
        const { store } = window.__xkit_store__;
        if (
          sessionId.indexOf('team-') === 0 &&
          sessionId === store.uiStore.selectedSession
        ) {
          // 选中的群有消息
          throttledGetDetailForCard(sessionId);
        }
      },
      onsysmsg: (msg) => {
        //@ts-ignore
        const store = Vue.prototype.$uikit.getStateContext().store;
        const handleBeRecallMsg = (scene) => {
          const { from, to, time } = msg;
          const idClient = msg.deletedIdClient;
          const opeAccount = msg.opeAccount;
          const fromAccount = opeAccount || from;
          const sessionId = store.sessionStore.getSessionId(scene, from, to);
          // 可能是多端同步过来的消息
          const isSelf = store.userStore.myUserInfo?store.userStore.myUserInfo.account === from:''
          // 插入一条对方撤回的自定义消息。
          const beRecallMsg = {
            idClient: `reCall-${idClient}`,
            target: to,
            flow: isSelf ? 'out' : 'in',
            sessionId,
            from: fromAccount,
            to,
            status: 'sent',
            scene,
            time,
            userUpdateTime: Date.now(),
            type: 'custom',
            feature: 'default',
            attach: {
              type: 'beReCallMsg',
            },
            ps: msg.ps,
          };

          console.log('beReCallMsg============', beRecallMsg);

          setTimeout(() => {
            store.msgStore.addMsg(sessionId, [beRecallMsg]);
          }, 0);
        };
        handleBeRecallMsg(msg.scene);
      },
      // onsysmsg: function(msg) {
      //   if (msg.type === 'deleteMsg') {
      //     let params;
      //     try {
      //       params = JSON.parse(localStorage.getItem('ImIdArr') || '[]');
      //       if (!Array.isArray(params)) params = []; 
      //     } catch (e) {
      //       params = []; 
      //     }
      //     params.push(msg.msg.idServer);
      //     localStorage.setItem('ImIdArr', JSON.stringify(params));
      //   }
      // }
      // shouldIgnoreNotification: () => {
      //   return true;
      // },
    };
    Vue.prototype.$uikit = new IMUIKit({
      initOptions,
      singleton: true,
      sdkVersion: 1,
      localOptions,
      funcOptions,
    });
    hasInit = true;
    resolve(true);
  });
};

export default function isIMToken() {
  return new Promise(async (resolve, reject) => {
    if (hasInit && localStorage.getItem('yximtoken')) {
      resolve(true);
    } else {
      hasInit = true;
      getImInfo()
        .then(async (res) => {
          if (res.code == 200) {
            const { accid, token } = res.data;
            localStorage.setItem(
              'yximtoken',
              JSON.stringify({ 'account': accid, 'token': token }),
            );
            let yximtoken = localStorage.getItem('yximtoken');
            await initIM(JSON.parse(yximtoken));
            return resolve(true);
          }
        })
        .catch(() => {
          hasInit = false;
          // 获取 token失败
          reject();
        });
    }
  });
}
